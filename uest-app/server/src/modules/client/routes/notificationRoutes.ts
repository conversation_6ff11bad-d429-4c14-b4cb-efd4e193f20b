import express from 'express';
import {
  getNotifications<PERSON><PERSON>roller,
  getUnread<PERSON>ount<PERSON><PERSON>roller,
  mark<PERSON><PERSON><PERSON><PERSON>ontroller,
  markAllAsReadController,
  deleteAllNotificationsController,
  getAdminNotificationsController,
  getAdminUnread<PERSON>ount<PERSON><PERSON>roller,
  markAd<PERSON><PERSON><PERSON><PERSON><PERSON>ontroller,
  markAllAdminAsReadController,
  deleteAllAdminNotificationsController,
  createSingleNotificationController,
  createAdminNotificationController,
  createLeaderboardNotificationController
} from '../controllers/notificationController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { studentAuthMiddleware } from '@/middlewares/studentAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const router = express.Router();

// Routes for Classes (using authClientMiddleware)
router.get('/classes', authClientMiddleware, getNotificationsController);
router.get('/classes/count', authClientMiddleware, getUnreadCount<PERSON>ontroller);
router.post('/classes/mark-read/:id', authClientMiddleware, markAsReadController);
router.post('/classes/mark-all-read', authClientMiddleware, markAllAsReadController);
router.delete('/classes/delete-all', authClientMiddleware, deleteAllNotificationsController);

// Routes for Students (using studentAuthMiddleware)
router.get('/students', studentAuthMiddleware, getNotificationsController);
router.get('/students/count', studentAuthMiddleware, getUnreadCountController);
router.post('/students/mark-read/:id', studentAuthMiddleware, markAsReadController);
router.post('/students/mark-all-read', studentAuthMiddleware, markAllAsReadController);
router.delete('/students/delete-all', studentAuthMiddleware, deleteAllNotificationsController);

// Routes for Admin (using authMiddleware)
router.get('/admin', authMiddleware, getAdminNotificationsController);
router.get('/admin/count', authMiddleware, getAdminUnreadCountController);
router.post('/admin/mark-read/:id', authMiddleware, markAdminAsReadController);
router.post('/admin/mark-all-read', authMiddleware, markAllAdminAsReadController);
router.delete('/admin/delete-all', authMiddleware, deleteAllAdminNotificationsController);

router.post('/create-single', createSingleNotificationController);
router.post('/create-admin', createAdminNotificationController);
router.post('/create-leaderboard', createLeaderboardNotificationController);

export default router;

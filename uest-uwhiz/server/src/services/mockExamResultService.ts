import prisma from "../config/prismaClient";
import { sendDailyQuizCompletionNotification } from "./dailyQuizNotificationService";

export const saveMockExamResult = async(data:any)=>{
    const result = await prisma.mockExamResult.create({data});

    // Send notification after saving result
    if (result && data.studentId && data.score !== undefined && data.coinEarnings !== undefined) {
        // Get streak information if available
        const streakRecord = await prisma.mockExamStreak.findFirst({
            where: { studentId: data.studentId },
            orderBy: { lastAttempt: "desc" },
        });

        const streakCount = streakRecord?.streakCount || 0;

        // Send completion notification asynchronously (don't wait for it)
        sendDailyQuizCompletionNotification({
            studentId: data.studentId,
            score: data.score,
            coinEarnings: data.coinEarnings,
            streakCount,
            isWeekly: data.isWeekly || false
        }).catch(error => {
            console.error('Failed to send daily quiz completion notification:', error);
        });
    }

    return result;
}

export const getMockExamCombinedData = async (
  studentId: string,
  page: number,
  limit: number,
  isWeekly?: boolean 
) => {
  const skip = (page - 1) * limit;

  const whereClause: any = { studentId };
  if (isWeekly !== undefined) {
    whereClause.isWeekly = isWeekly; 
  }

  const mockExamResults = await prisma.mockExamResult.findMany({
    where: whereClause,
    skip,
    take: limit,
    orderBy: { createdAt: "desc" },
  });

  const totalMockExamResults = await prisma.mockExamResult.count({
    where: whereClause,
  });

  const totalPages = Math.ceil(totalMockExamResults / limit);
  const streakRecord = await prisma.mockExamStreak.findFirst({
    where: { studentId },
    orderBy: { lastAttempt: "desc" },
  });

  const streak = streakRecord
    ? {
        streakCount: streakRecord.streakCount,
        lastAttempt: streakRecord.lastAttempt,
      }
    : {
        streakCount: 0,
        lastAttempt: null,
      };
  const badgeImages = {
    PerfectMonth: "/Perfect Month.svg",
    PerfectYear: "/Perfect Year.svg",
    DailyStreak: "/Streak.svg",
  };

  const badgeAlts = {
    PerfectMonth: "Perfect Month Badge",
    PerfectYear: "Perfect Year Badge",
    DailyStreak: "Daily Streak Badge",
  };

  const streakCount = streak.streakCount;
  const badges = [];

  if (streakCount > 0) {
    badges.push({
      badgeType: "DailyStreak",
      badgeSrc: badgeImages.DailyStreak,
      badgeAlt: `${badgeAlts.DailyStreak} - ${streakCount} days`,
      count: streakCount,
    });
  }
  if (streakCount >= 30) {
    badges.push({
      badgeType: "PerfectMonth",
      badgeSrc: badgeImages.PerfectMonth,
      badgeAlt: badgeAlts.PerfectMonth,
    });
  }
  if (streakCount >= 365) {
    badges.push({
      badgeType: "PerfectYear",
      badgeSrc: badgeImages.PerfectYear,
      badgeAlt: badgeAlts.PerfectYear,
    });
  }

  const mainBadge = badges.length > 0 ? badges[0] : null;

  const badge = {
    streakCount,
    badges
  };

  return {
    mockExamResults,
    streak,
    badge,
    pagination: {
      totalResults: totalMockExamResults,
      totalPages,
      currentPage: page,
      limit,
    },
  };
};
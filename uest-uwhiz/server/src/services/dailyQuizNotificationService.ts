import axios from 'axios';

export interface DailyQuizNotificationData {
  studentId: string;
  score: number;
  coinEarnings: number;
  streakCount?: number;
  isWeekly?: boolean;
}

export const sendDailyQuizCompletionNotification = async (data: DailyQuizNotificationData) => {
  try {
    const { studentId, score, coinEarnings, streakCount, isWeekly = false } = data;
    
    const title = isWeekly ? 'Weekly Quiz Completed! 🎉' : 'Daily Quiz Completed! 🎉';
    const streakText = streakCount ? ` Your streak: ${streakCount} ${isWeekly ? 'weeks' : 'days'}!` : '';
    const message = `Great job! You scored ${score}% and earned ${coinEarnings} coins.${streakText}`;

    // Send notification to main backend
    const response = await axios.post(`${process.env.UEST_BACKEND_URL}/notifications/create-single`, {
      userId: studentId,
      userType: 'STUDENT',
      type: 'STUDENT_DAILY_QUIZ_COMPLETED',
      title,
      message,
      data: {
        actionType: 'OPEN_DAILY_QUIZ_RESULTS',
        score,
        coinEarnings,
        streakCount,
        isWeekly,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Daily quiz completion notification sent for student ${studentId}:`, response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('Failed to send daily quiz completion notification:', error);
    return { 
      success: false, 
      error: error.response?.data?.message || error.message || 'Failed to send notification' 
    };
  }
};

export const sendDailyQuizResultNotification = async (data: DailyQuizNotificationData & { rank?: number }) => {
  try {
    const { studentId, score, coinEarnings, streakCount, rank, isWeekly = false } = data;
    
    const title = isWeekly ? 'Weekly Quiz Results Available! 📊' : 'Daily Quiz Results Available! 📊';
    const rankText = rank ? ` You ranked #${rank} today!` : '';
    const message = `Your ${isWeekly ? 'weekly' : 'daily'} quiz results: ${score}% score, ${coinEarnings} coins earned.${rankText}`;

    // Send notification to main backend
    const response = await axios.post(`${process.env.UEST_BACKEND_URL}/notifications/create-single`, {
      userId: studentId,
      userType: 'STUDENT',
      type: 'STUDENT_DAILY_QUIZ_RESULT',
      title,
      message,
      data: {
        actionType: 'OPEN_DAILY_QUIZ_RESULTS',
        score,
        coinEarnings,
        streakCount,
        rank,
        isWeekly,
        timestamp: new Date().toISOString(),
      },
    });

    console.log(`Daily quiz result notification sent for student ${studentId}:`, response.data);
    return { success: true, data: response.data };
  } catch (error: any) {
    console.error('Failed to send daily quiz result notification:', error);
    return { 
      success: false, 
      error: error.response?.data?.message || error.message || 'Failed to send notification' 
    };
  }
};
